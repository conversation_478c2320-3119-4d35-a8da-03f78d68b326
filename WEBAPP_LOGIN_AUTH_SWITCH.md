# WebApp登录验证开关功能

## 功能说明

根据提交记录 `1baf79c90c025caf127a5cdfa902a53cc79f8e67` 的登录验证逻辑优化，为启动聊天的web页面增加了webapp登录验证的开关功能。

## 配置说明

### 环境变量配置

在 `api/.env` 文件中添加以下配置：

```bash
# 是否禁用webapp登录验证（二开新增配置）
WEBAPP_LOGIN_DISABLED=false
```

- `WEBAPP_LOGIN_DISABLED=false` (默认值): 启用webapp登录验证，用户需要有效的console_token才能访问聊天页面
- `WEBAPP_LOGIN_DISABLED=true`: 禁用webapp登录验证，用户可以直接访问聊天页面而无需登录

### 后端实现

1. **配置定义** (`api/configs/extend/__init__.py`):
   ```python
   WEBAPP_LOGIN_DISABLED: bool = Field(
       description="是否禁用webapp登录验证",
       default=False,
   )
   ```

2. **系统特性服务** (`api/services/feature_service.py`):
   ```python
   # 根据WEBAPP_LOGIN_DISABLED配置决定是否启用webapp认证
   system_features.webapp_auth.enabled = not dify_config.WEBAPP_LOGIN_DISABLED
   ```

### 前端实现

修改了以下聊天组件来检查 `systemFeatures.webapp_auth.enabled` 配置：

1. **Chat组件** (`web/app/components/base/chat/chat/index.tsx`)
2. **ChatWithHistory组件** (`web/app/components/base/chat/chat-with-history/index.tsx`)
3. **EmbeddedChatbot组件** (`web/app/components/base/chat/embedded-chatbot/index.tsx`)
4. **TextGeneration组件** (`web/app/components/share/text-generation/index.tsx`)

### 逻辑流程

```typescript
// 只有在启用webapp登录验证时才检查token
if (systemFeatures.webapp_auth.enabled) {
  const consoleToken = searchParams.get('console_token')
  const consoleTokenFromLocalStorage = localStorage?.getItem('console_token')

  if (!(consoleToken || consoleTokenFromLocalStorage)) {
    // 重定向到登录页面
    localStorage?.setItem('redirect_url', window.location.href)
    router.replace('/signin')
    return null
  }
}
```

## 使用场景

### 场景1: 启用登录验证 (默认)
- 配置: `WEBAPP_LOGIN_DISABLED=false`
- 行为: 用户访问聊天页面时需要提供有效的console_token
- 适用: 需要用户认证的生产环境

### 场景2: 禁用登录验证
- 配置: `WEBAPP_LOGIN_DISABLED=true`
- 行为: 用户可以直接访问聊天页面，无需登录验证
- 适用: 开发测试环境或公开访问的聊天应用

## 测试方法

1. **访问测试页面**: `http://127.0.0.1:3001/test-webapp-auth`
   - 查看当前配置状态和系统特性

2. **访问聊天页面**: `http://127.0.0.1:3001/chat/rVTIiziVxbUmh8TW`
   - 测试登录验证是否按预期工作

3. **切换配置测试**:
   - 修改 `WEBAPP_LOGIN_DISABLED` 值
   - 重启服务
   - 验证行为变化

## 注意事项

1. 修改配置后需要重启API服务器才能生效
2. 该功能基于现有的企业版webapp认证框架
3. 前端通过 `systemFeatures.webapp_auth.enabled` 来判断是否需要验证登录
4. 保持与原有登录验证逻辑的兼容性

## 相关文件

- `api/.env` - 环境变量配置
- `api/configs/extend/__init__.py` - 配置定义
- `api/services/feature_service.py` - 系统特性服务
- `web/app/components/base/chat/*/index.tsx` - 前端聊天组件
